<?php
require_once '../config/config.php';
require_once '../includes/Database.php';

// Check if user is logged in as admin
require_admin();

$db = new Database();

echo "<h2>Image Import Tables Debug</h2>";

// Check if tables exist
echo "<h3>Table Existence Check:</h3>";
try {
    $jobsExists = $db->tableExists('image_import_jobs');
    $filesExists = $db->tableExists('image_import_files');
    
    echo "<p><strong>image_import_jobs:</strong> " . ($jobsExists ? '✅ EXISTS' : '❌ NOT EXISTS') . "</p>";
    echo "<p><strong>image_import_files:</strong> " . ($filesExists ? '✅ EXISTS' : '❌ NOT EXISTS') . "</p>";
    
    if ($jobsExists && $filesExists) {
        echo "<p style='color: green;'><strong>✅ Both tables exist - Image import should work!</strong></p>";
    } else {
        echo "<p style='color: red;'><strong>❌ Tables missing - Need to run setup</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Show all tables in database
echo "<h3>All Tables in Database:</h3>";
try {
    $tables = $db->fetchAll("SHOW TABLES");
    echo "<ul>";
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        echo "<li>" . htmlspecialchars($tableName);
        if (strpos($tableName, 'image_import') !== false) {
            echo " <strong style='color: blue;'>(IMAGE IMPORT TABLE)</strong>";
        }
        echo "</li>";
    }
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error getting tables:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test table creation SQL
echo "<h3>Test Table Creation:</h3>";
if (isset($_GET['create']) && $_GET['create'] === '1') {
    try {
        // Create tables directly
        $sql1 = "CREATE TABLE IF NOT EXISTS `image_import_jobs` (
          `id` varchar(50) NOT NULL,
          `userId` varchar(50) NOT NULL,
          `matchingMethod` varchar(50) NOT NULL DEFAULT 'filename',
          `overwriteExisting` tinyint(1) NOT NULL DEFAULT 0,
          `status` varchar(50) NOT NULL DEFAULT 'PENDING',
          `totalFiles` int(11) DEFAULT 0,
          `processedFiles` int(11) DEFAULT 0,
          `matchedFiles` int(11) DEFAULT 0,
          `errorFiles` int(11) DEFAULT 0,
          `skippedFiles` int(11) DEFAULT 0,
          `startTime` datetime DEFAULT NULL,
          `endTime` datetime DEFAULT NULL,
          `errorMessage` text DEFAULT NULL,
          `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_image_import_jobs_userId` (`userId`),
          KEY `idx_image_import_jobs_status` (`status`),
          KEY `idx_image_import_jobs_createdAt` (`createdAt`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $sql2 = "CREATE TABLE IF NOT EXISTS `image_import_files` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `jobId` varchar(50) NOT NULL,
          `originalName` varchar(255) NOT NULL,
          `filePath` varchar(500) NOT NULL,
          `status` varchar(50) NOT NULL DEFAULT 'PENDING',
          `bookId` varchar(50) DEFAULT NULL,
          `accessionNo` varchar(100) DEFAULT NULL,
          `errorMessage` text DEFAULT NULL,
          `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_image_import_files_jobId` (`jobId`),
          KEY `idx_image_import_files_status` (`status`),
          KEY `idx_image_import_files_bookId` (`bookId`),
          KEY `idx_image_import_files_accessionNo` (`accessionNo`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($sql1);
        echo "<p style='color: green;'>✅ Created image_import_jobs table</p>";
        
        $db->query($sql2);
        echo "<p style='color: green;'>✅ Created image_import_files table</p>";
        
        // Add foreign keys separately
        try {
            $fk1 = "ALTER TABLE `image_import_files` ADD CONSTRAINT `fk_image_import_files_jobId` FOREIGN KEY (`jobId`) REFERENCES `image_import_jobs` (`id`) ON DELETE CASCADE";
            $db->query($fk1);
            echo "<p style='color: green;'>✅ Added foreign key constraint for jobId</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Foreign key constraint already exists or failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        try {
            $fk2 = "ALTER TABLE `image_import_files` ADD CONSTRAINT `fk_image_import_files_bookId` FOREIGN KEY (`bookId`) REFERENCES `books` (`id`) ON DELETE SET NULL";
            $db->query($fk2);
            echo "<p style='color: green;'>✅ Added foreign key constraint for bookId</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Foreign key constraint already exists or failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "<p><strong>✅ Table creation completed! <a href='debug_image_tables.php'>Refresh to check</a></strong></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'><strong>❌ Error creating tables:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    }
} else {
    echo "<p><a href='debug_image_tables.php?create=1' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🛠️ Create Tables Now</a></p>";
}

echo "<hr>";
echo "<p><a href='import-images.php'>← Back to Image Import</a> | <a href='setup_image_import.php'>Setup Page</a></p>";
?>
