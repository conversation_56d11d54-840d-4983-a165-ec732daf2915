<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Configuration File
 */

// Prevent direct access
if (!defined('MAITHILI_LIBRARY')) {
    define('MAITHILI_LIBRARY', true);
}

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Kathmandu');

// Database Configuration - Auto-configured for XAMPP
define('DB_HOST', 'localhost');
define('DB_NAME', 'maithili_library');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Application Configuration
define('APP_NAME', 'मैथिली विकास कोष - <PERSON><PERSON><PERSON>ikas <PERSON>');
define('APP_SUBTITLE', 'विद्यापति पुस्तकालय - अध्ययन तथा अनुसंधान केन्<PERSON>्र');
define('APP_SUBTITLE_EN', 'Vidyapati Library - Study and Research Center');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/library/maithili-library-web');
define('ADMIN_URL', APP_URL . '/admin');

// File Upload Configuration
define('UPLOAD_DIR', __DIR__ . '/../uploads/');
define('UPLOAD_URL', APP_URL . '/uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB per file
define('MAX_TOTAL_UPLOAD_SIZE', 100 * 1024 * 1024); // 100MB total
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'webp']);
define('THUMBNAIL_WIDTH', 300);
define('THUMBNAIL_HEIGHT', 400);

// Pagination Configuration
define('BOOKS_PER_PAGE', 24);
define('ADMIN_ITEMS_PER_PAGE', 20);

// Security Configuration
define('SESSION_LIFETIME', 3600 * 8); // 8 hours
define('ADMIN_SESSION_LIFETIME', 3600 * 4); // 4 hours
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Application Colors (Your specified theme)
define('PRIMARY_COLOR', '#55080C');        // Deep burgundy red
define('SECONDARY_COLOR', '#FBF7F8');      // Very light pink/off-white
define('ACCENT_COLOR', '#8B1538');         // Medium burgundy red
define('LIGHT_ACCENT', '#D4A574');         // Warm gold/beige
define('TEXT_DARK', '#2C1810');            // Dark brown
define('TEXT_LIGHT', '#6B4E3D');           // Medium brown
define('SUCCESS_COLOR', '#2D5016');        // Dark green
define('WARNING_COLOR', '#B8860B');        // Dark goldenrod

// Contact Information
define('CONTACT_DEVELOPER', 'श्रद्धेय वात्स्यायन');
define('CONTACT_PHONE', '9844361480');
define('CONTACT_EMAIL', '<EMAIL>');
define('COPYRIGHT_YEAR', date('Y'));

// Image Optimization Settings
define('IMAGE_QUALITY', 85);
define('WEBP_QUALITY', 80);
define('THUMBNAIL_QUALITY', 75);

// Cache Settings
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 3600); // 1 hour

// Language Settings
define('DEFAULT_LANGUAGE', 'ne'); // Nepali/Maithili
define('SUPPORTED_LANGUAGES', ['ne', 'en']);

// Font Configuration for Devanagari Support
define('DEVANAGARI_FONT_PRIMARY', 'Noto Sans Devanagari');
define('DEVANAGARI_FONT_FALLBACK', 'Mangal, Nirmala UI, sans-serif');
define('ENGLISH_FONT_PRIMARY', 'Inter');
define('ENGLISH_FONT_FALLBACK', '-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif');
define('FONT_ASSETS_PATH', '/assets/fonts/');
define('FONT_CACHE_DURATION', 31536000); // 1 year for font caching

// Icon and Logo Configuration
define('ICON_PATH', '/assets/images/icon.png');
define('LOGO_PATH', '/assets/images/logo.png');
define('FAVICON_PATH', '/assets/images/icon.png');
define('ICON_SIZES', [16, 32, 48, 64, 128, 256, 512]); // Standard icon sizes
define('ICON_CACHE_DURATION', 31536000); // 1 year for icon caching
define('DEFAULT_ICON_SIZE', 64);
define('LOGO_MAX_WIDTH', 200);
define('LOGO_MAX_HEIGHT', 80);

// Admin User Roles
define('ROLE_SUPER_ADMIN', 'SUPER_ADMIN');
define('ROLE_ADMIN', 'ADMIN');
define('ROLE_LIBRARIAN', 'LIBRARIAN');
define('ROLE_ASSISTANT', 'ASSISTANT');
define('ROLE_VIEWER', 'VIEWER');

// Book Status
define('BOOK_STATUS_AVAILABLE', 'AVAILABLE');
define('BOOK_STATUS_ISSUED', 'ISSUED');
define('BOOK_STATUS_RESERVED', 'RESERVED');
define('BOOK_STATUS_MAINTENANCE', 'MAINTENANCE');
define('BOOK_STATUS_LOST', 'LOST');

// Member Types
define('MEMBER_TYPE_REGULAR', 'REGULAR');
define('MEMBER_TYPE_STUDENT', 'STUDENT');
define('MEMBER_TYPE_FACULTY', 'FACULTY');
define('MEMBER_TYPE_SENIOR', 'SENIOR');
define('MEMBER_TYPE_LIFETIME', 'LIFETIME');

// Borrowing Settings
define('DEFAULT_BORROW_DAYS', 14);
define('MAX_BOOKS_PER_MEMBER', 5);
define('FINE_PER_DAY', 5.00); // NPR

// Email Configuration (if needed)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', APP_NAME);

// Backup Configuration
define('BACKUP_DIR', __DIR__ . '/../backups/');
define('AUTO_BACKUP_ENABLED', true);
define('BACKUP_RETENTION_DAYS', 30);

// Logging Configuration
define('LOG_DIR', __DIR__ . '/../logs/');
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// Development/Production Mode
define('DEVELOPMENT_MODE', false); // Set to false in production
define('DEBUG_MODE', DEVELOPMENT_MODE);

// Create necessary directories
$directories = [
    UPLOAD_DIR,
    UPLOAD_DIR . 'books/',
    UPLOAD_DIR . 'thumbs/',
    UPLOAD_DIR . 'temp/',
    dirname(BACKUP_DIR),
    BACKUP_DIR,
    dirname(LOG_DIR),
    LOG_DIR
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Auto-load common functions
require_once __DIR__ . '/../includes/functions.php';

// Start session if not already started (skip during setup)
if (session_status() === PHP_SESSION_NONE && !defined('SETUP_MODE')) {
    session_start();
}

// Set session timeout
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    $timeout = ADMIN_SESSION_LIFETIME;
} else {
    $timeout = SESSION_LIFETIME;
}

if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $timeout)) {
    session_unset();
    session_destroy();
    if (isset($_SESSION['admin_logged_in'])) {
        header('Location: ' . ADMIN_URL . '/login.php?timeout=1');
        exit;
    }
}

$_SESSION['last_activity'] = time();

// Helper function to get asset URL with version for cache busting
function asset_url($path) {
    $file_path = __DIR__ . '/../assets/' . $path;
    $version = file_exists($file_path) ? filemtime($file_path) : time();
    return APP_URL . '/assets/' . $path . '?v=' . $version;
}

// Helper function to get upload URL
function upload_url($path) {
    return UPLOAD_URL . $path;
}

// Helper function to check if user is admin
function is_admin() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

// Helper function to check admin permissions
function has_permission($permission) {
    if (!is_admin()) {
        return false;
    }
    
    $user_permissions = $_SESSION['admin_permissions'] ?? [];
    return in_array($permission, $user_permissions) || $_SESSION['admin_role'] === ROLE_SUPER_ADMIN;
}

// Helper function to require admin login
function require_admin() {
    if (!is_admin()) {
        header('Location: ' . ADMIN_URL . '/login.php');
        exit;
    }
}

// Helper function to require specific permission
function require_permission($permission) {
    require_admin();
    if (!has_permission($permission)) {
        header('Location: ' . ADMIN_URL . '/dashboard.php?error=insufficient_permissions');
        exit;
    }
}

// Helper function to sanitize input
function sanitize_input($input) {
    if (is_array($input)) {
        return array_map('sanitize_input', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Helper function to validate CSRF token
function validate_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Helper function to generate CSRF token
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Helper function to format Nepali text
function format_nepali_text($text) {
    if (empty($text)) return '';
    return '<span class="nepali-text">' . htmlspecialchars($text) . '</span>';
}

// Helper function to format English text
function format_english_text($text) {
    if (empty($text)) return '';
    return '<span class="english-text">' . htmlspecialchars($text) . '</span>';
}

// Helper function to get display name (Nepali first, then English)
function get_display_name($nepali_name, $english_name) {
    return !empty($nepali_name) ? $nepali_name : $english_name;
}

// Helper function to format currency
function format_currency($amount, $currency = 'NPR') {
    if ($currency === 'NPR') {
        return 'रू ' . number_format($amount, 2);
    }
    return $currency . ' ' . number_format($amount, 2);
}

// Helper function to format date in Nepali style
function format_nepali_date($date) {
    if (empty($date)) return '';
    return date('Y/m/d', strtotime($date));
}

// Helper function to get font URL with cache busting
function font_url($font_file) {
    $font_path = __DIR__ . '/../assets/fonts/' . $font_file;
    $version = file_exists($font_path) ? filemtime($font_path) : time();
    return APP_URL . '/assets/fonts/' . $font_file . '?v=' . $version;
}

// Helper function to check if Devanagari fonts are loaded
function are_devanagari_fonts_loaded() {
    $regular_font = __DIR__ . '/../assets/fonts/NotoSansDevanagari-Regular.ttf';
    $bold_font = __DIR__ . '/../assets/fonts/NotoSansDevanagari-Bold.ttf';
    return file_exists($regular_font) && file_exists($bold_font);
}

// Helper function to get appropriate CSS class for text content
function get_text_css_class($text, $force_language = null) {
    if ($force_language === 'ne' || $force_language === 'devanagari') {
        return 'nepali-text';
    }
    if ($force_language === 'en' || $force_language === 'english') {
        return 'english-text';
    }

    // Auto-detect based on Unicode ranges
    if (preg_match('/[\x{0900}-\x{097F}]/u', $text)) {
        return 'nepali-text'; // Contains Devanagari characters
    }

    return 'english-text';
}

// Helper function to get icon URL with cache busting
function icon_url($size = null) {
    $base_url = rtrim(APP_URL, '/');

    // If size is specified, try to use the sized icon first
    if ($size && is_numeric($size)) {
        $sized_icon_path = __DIR__ . "/../assets/images/icon-{$size}x{$size}.png";
        if (file_exists($sized_icon_path)) {
            $version = filemtime($sized_icon_path);
            return $base_url . "/assets/images/icon-{$size}x{$size}.png?v=" . $version;
        }
    }

    // Fallback to main icon
    $icon_path = __DIR__ . '/../assets/images/icon.png';
    $version = file_exists($icon_path) ? filemtime($icon_path) : time();
    return $base_url . '/assets/images/icon.png?v=' . $version;
}

// Helper function to get logo URL with cache busting
function logo_url() {
    $logo_path = __DIR__ . '/../assets/images/logo.png';
    $version = file_exists($logo_path) ? filemtime($logo_path) : time();

    // Use relative path from the web root
    $base_url = rtrim(APP_URL, '/');
    $logo_relative_path = '/assets/images/logo.png';
    return $base_url . $logo_relative_path . '?v=' . $version;
}

// Helper function to get favicon URL
function favicon_url() {
    return icon_url(32); // Standard favicon size
}

// Helper function to generate favicon HTML tags
function generate_favicon_tags() {
    $html = '';
    $base_url = rtrim(APP_URL, '/');

    // Standard favicon.ico for legacy browser support
    $favicon_ico_path = __DIR__ . '/../assets/images/favicon.ico';
    if (file_exists($favicon_ico_path)) {
        $version = filemtime($favicon_ico_path);
        $html .= '<link rel="icon" type="image/x-icon" href="' . $base_url . '/assets/images/favicon.ico?v=' . $version . '">' . "\n";
    }

    // Standard favicon PNG
    $html .= '<link rel="icon" type="image/png" href="' . favicon_url() . '">' . "\n";

    // Apple touch icons
    $html .= '<link rel="apple-touch-icon" href="' . icon_url() . '">' . "\n";

    // Standard sizes for different devices
    $standard_sizes = [16, 32, 48, 96, 192];
    foreach ($standard_sizes as $size) {
        $html .= '<link rel="icon" type="image/png" sizes="' . $size . 'x' . $size . '" href="' . icon_url($size) . '">' . "\n";
    }

    return $html;
}

// Helper function to check if icon files exist
function are_icons_loaded() {
    $icon_path = __DIR__ . '/../assets/images/icon.png';
    $logo_path = __DIR__ . '/../assets/images/logo.png';
    return file_exists($icon_path) && file_exists($logo_path);
}

// Helper function to log activities
function log_activity($message, $level = 'INFO') {
    if (!DEBUG_MODE && $level === 'DEBUG') return;
    
    $log_file = LOG_DIR . 'app_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $user = $_SESSION['admin_username'] ?? 'guest';
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    $log_entry = "[$timestamp] [$level] [$user@$ip] $message" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}
?>
