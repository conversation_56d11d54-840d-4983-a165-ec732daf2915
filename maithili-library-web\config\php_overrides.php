<?php
/**
 * PHP Configuration Overrides for Image Upload
 * This file attempts to override PHP settings at runtime
 */

// Attempt to increase upload limits (may not work on all servers)
if (function_exists('ini_set')) {
    // Upload limits
    ini_set('upload_max_filesize', '100M');
    ini_set('post_max_size', '100M');
    ini_set('max_file_uploads', '50');
    
    // Execution time limits
    ini_set('max_execution_time', '300');
    ini_set('max_input_time', '300');
    
    // Memory limit (if needed)
    $current_memory = ini_get('memory_limit');
    $current_memory_bytes = convertToBytes($current_memory);
    $required_memory_bytes = 256 * 1024 * 1024; // 256MB
    
    if ($current_memory_bytes < $required_memory_bytes) {
        ini_set('memory_limit', '256M');
    }
}

/**
 * Convert memory size string to bytes
 */
function convertToBytes($value) {
    $value = trim($value);
    $last = strtolower($value[strlen($value)-1]);
    $value = (int) $value;
    
    switch($last) {
        case 'g':
            $value *= 1024;
        case 'm':
            $value *= 1024;
        case 'k':
            $value *= 1024;
    }
    
    return $value;
}

// Log the configuration attempt
if (defined('LOG_DIR')) {
    $log_message = sprintf(
        "[%s] PHP Config Override - upload_max_filesize: %s, post_max_size: %s, max_execution_time: %s\n",
        date('Y-m-d H:i:s'),
        ini_get('upload_max_filesize'),
        ini_get('post_max_size'),
        ini_get('max_execution_time')
    );
    
    file_put_contents(LOG_DIR . 'php_config.log', $log_message, FILE_APPEND | LOCK_EX);
}
?>
