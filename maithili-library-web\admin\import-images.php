<?php
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/Database.php';

// Check if user is logged in as admin
require_admin();

$db = new Database();
$error = '';
$success = '';
$action = $_GET['action'] ?? 'upload';
$job_id = $_GET['job_id'] ?? null;

// Check for setup success message
if (isset($_GET['setup']) && $_GET['setup'] === 'success') {
    $success = 'Image import tables created successfully! You can now upload images.';
}

// Enhanced error handling and validation
function validateImageUpload($files) {
    $errors = [];

    if (!isset($files['image_files']) || empty($files['image_files']['tmp_name'][0])) {
        $errors[] = 'No files uploaded. Please select at least one image file.';
        return $errors;
    }

    $uploadedFiles = $files['image_files'];
    $totalFiles = count($uploadedFiles['name']);
    $maxFiles = 50; // Reasonable limit

    if ($totalFiles > $maxFiles) {
        $errors[] = "Too many files uploaded. Maximum allowed: $maxFiles files.";
    }

    for ($i = 0; $i < $totalFiles; $i++) {
        if ($uploadedFiles['error'][$i] !== UPLOAD_ERR_OK) {
            $fileName = $uploadedFiles['name'][$i] ?? "File $i";
            switch ($uploadedFiles['error'][$i]) {
                case UPLOAD_ERR_INI_SIZE:
                case UPLOAD_ERR_FORM_SIZE:
                    $errors[] = "File '$fileName' is too large. Maximum size: " . (MAX_FILE_SIZE / 1024 / 1024) . "MB";
                    break;
                case UPLOAD_ERR_PARTIAL:
                    $errors[] = "File '$fileName' was only partially uploaded. Please try again.";
                    break;
                case UPLOAD_ERR_NO_FILE:
                    continue 2; // Skip this file
                case UPLOAD_ERR_NO_TMP_DIR:
                    $errors[] = "Server error: No temporary directory available.";
                    break;
                case UPLOAD_ERR_CANT_WRITE:
                    $errors[] = "Server error: Cannot write file to disk.";
                    break;
                default:
                    $errors[] = "Unknown error occurred while uploading '$fileName'.";
            }
        }

        // Validate file type
        if (isset($uploadedFiles['name'][$i]) && !is_allowed_image($uploadedFiles['name'][$i])) {
            $errors[] = "File '{$uploadedFiles['name'][$i]}' is not a valid image format. Allowed: JPG, PNG, WebP";
        }

        // Validate file size
        if (isset($uploadedFiles['size'][$i]) && $uploadedFiles['size'][$i] > MAX_FILE_SIZE) {
            $fileName = $uploadedFiles['name'][$i] ?? "File $i";
            $errors[] = "File '$fileName' exceeds maximum size of " . (MAX_FILE_SIZE / 1024 / 1024) . "MB";
        }
    }

    return $errors;
}

function validateImageImportTables($db) {
    try {
        if (!$db->tableExists('image_import_jobs')) {
            return 'Image import jobs table does not exist. Please run the setup first.';
        }

        if (!$db->tableExists('image_import_files')) {
            return 'Image import files table does not exist. Please run the setup first.';
        }

        // Test table accessibility
        $db->query("SELECT 1 FROM image_import_jobs LIMIT 1");
        $db->query("SELECT 1 FROM image_import_files LIMIT 1");

        return null; // No errors
    } catch (Exception $e) {
        log_activity("Image import table validation failed: " . $e->getMessage(), 'ERROR');
        return 'Database tables are not accessible: ' . $e->getMessage();
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $postAction = $_POST['action'] ?? '';

        if ($postAction === 'upload') {
            // Validate tables first
            $tableError = validateImageImportTables($db);
            if ($tableError) {
                throw new Exception($tableError);
            }

            // Validate file upload
            $uploadErrors = validateImageUpload($_FILES);
            if (!empty($uploadErrors)) {
                throw new Exception(implode(' ', $uploadErrors));
            }

            $uploadedFiles = $_FILES['image_files'];
            $matchingMethod = $_POST['matching_method'] ?? 'filename';
            $overwriteExisting = isset($_POST['overwrite_existing']);
            
            // Create import job record
            $jobId = 'img' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
            
            $db->insert('image_import_jobs', [
                'id' => $jobId,
                'userId' => $_SESSION['admin_user_id'],
                'matchingMethod' => $matchingMethod,
                'overwriteExisting' => $overwriteExisting ? 1 : 0,
                'status' => 'PENDING',
                'createdAt' => date('Y-m-d H:i:s'),
                'updatedAt' => date('Y-m-d H:i:s')
            ]);

            // Process uploaded files
            $uploadDir = UPLOAD_DIR . 'temp_images/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $fileCount = 0;
            $totalFiles = count($uploadedFiles['name']);
            
            for ($i = 0; $i < $totalFiles; $i++) {
                if ($uploadedFiles['error'][$i] === UPLOAD_ERR_OK) {
                    $originalName = $uploadedFiles['name'][$i];
                    $tempPath = $uploadedFiles['tmp_name'][$i];
                    
                    // Validate file type
                    if (!is_allowed_image($originalName)) {
                        continue;
                    }
                    
                    // Generate unique filename
                    $fileName = $jobId . '_' . $i . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $originalName);
                    $filePath = $uploadDir . $fileName;
                    
                    if (move_uploaded_file($tempPath, $filePath)) {
                        // Store file info in database
                        $db->insert('image_import_files', [
                            'jobId' => $jobId,
                            'originalName' => $originalName,
                            'filePath' => $filePath,
                            'status' => 'PENDING',
                            'createdAt' => date('Y-m-d H:i:s')
                        ]);
                        $fileCount++;
                    }
                }
            }

            // Update job with file count
            $db->update('image_import_jobs', ['totalFiles' => $fileCount], ['id' => $jobId]);

            $success = "Successfully uploaded $fileCount files. Processing will begin shortly.";
            header("Location: import-images.php?action=status&job_id=" . urlencode($jobId));
            exit;
            
        } elseif ($postAction === 'process' && $job_id) {
            // Start processing job
            $db->update('image_import_jobs', [
                'status' => 'PROCESSING',
                'startTime' => date('Y-m-d H:i:s'),
                'updatedAt' => date('Y-m-d H:i:s')
            ], ['id' => $job_id]);
            
            // Process in background (simplified for demo)
            processImageImportJob($job_id);
            
            header("Location: import-images.php?action=status&job_id=" . urlencode($job_id));
            exit;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Check if image import tables exist
$tablesExist = false;
try {
    $tablesExist = $db->tableExists('image_import_jobs') && $db->tableExists('image_import_files');
} catch (Exception $e) {
    // If there's an error checking tables, assume they don't exist
    $tablesExist = false;
}

// Get import job details if viewing status
$importJob = null;
if ($action === 'status' && $job_id && $tablesExist) {
    $importJob = $db->fetch("SELECT * FROM image_import_jobs WHERE id = ?", [$job_id]);
    if ($importJob) {
        // Get associated files
        $importFiles = $db->fetchAll("SELECT * FROM image_import_files WHERE jobId = ?", [$job_id]);
        $importJob['files'] = $importFiles;
    }
}

// Get recent import jobs
$recentJobs = [];
if ($tablesExist) {
    $recentJobs = $db->fetchAll("
        SELECT * FROM image_import_jobs
        ORDER BY createdAt DESC
        LIMIT 10
    ");
}

/**
 * Process image import job with enhanced error handling
 */
function processImageImportJob($jobId) {
    global $db;

    try {
        // Validate job ID
        if (empty($jobId) || !is_string($jobId)) {
            throw new Exception('Invalid job ID provided');
        }

        // Get job details with error handling
        $job = $db->fetch("SELECT * FROM image_import_jobs WHERE id = ?", [$jobId]);
        if (!$job) {
            log_activity("Image import job not found: $jobId", 'ERROR');
            throw new Exception('Job not found: ' . $jobId);
        }

        // Validate job status
        if ($job['status'] !== 'PENDING' && $job['status'] !== 'PROCESSING') {
            log_activity("Attempted to process job with invalid status: {$job['status']}", 'WARNING');
            throw new Exception('Job cannot be processed. Current status: ' . $job['status']);
        }

        // Get files with error handling
        $files = $db->fetchAll("SELECT * FROM image_import_files WHERE jobId = ?", [$jobId]);
        if (empty($files)) {
            log_activity("No files found for job: $jobId", 'WARNING');
            throw new Exception('No files found for this import job');
        }

        $stats = [
            'processed' => 0,
            'matched' => 0,
            'errors' => 0,
            'skipped' => 0
        ];

        log_activity("Starting image import job processing: $jobId with " . count($files) . " files", 'INFO');
        
        foreach ($files as $file) {
            try {
                $stats['processed']++;

                // Validate file record
                if (empty($file['originalName']) || empty($file['filePath'])) {
                    $stats['errors']++;
                    $db->update('image_import_files', [
                        'status' => 'ERROR',
                        'errorMessage' => 'Invalid file record: missing name or path'
                    ], ['id' => $file['id']]);
                    continue;
                }

                // Check if file still exists
                if (!file_exists($file['filePath'])) {
                    $stats['errors']++;
                    $db->update('image_import_files', [
                        'status' => 'ERROR',
                        'errorMessage' => 'File not found on disk: ' . basename($file['filePath'])
                    ], ['id' => $file['id']]);
                    continue;
                }

                // Validate file size and type
                $fileSize = filesize($file['filePath']);
                if ($fileSize === false || $fileSize === 0) {
                    $stats['errors']++;
                    $db->update('image_import_files', [
                        'status' => 'ERROR',
                        'errorMessage' => 'File is empty or unreadable'
                    ], ['id' => $file['id']]);
                    continue;
                }

                if ($fileSize > MAX_FILE_SIZE) {
                    $stats['skipped']++;
                    $db->update('image_import_files', [
                        'status' => 'SKIPPED',
                        'errorMessage' => 'File too large: ' . round($fileSize / 1024 / 1024, 1) . 'MB (max: ' . round(MAX_FILE_SIZE / 1024 / 1024, 1) . 'MB)'
                    ], ['id' => $file['id']]);
                    continue;
                }

                // Extract accession number from filename
                $accessionNo = extractAccessionFromFilename($file['originalName'], $job['matchingMethod']);

                if (!$accessionNo) {
                    $stats['skipped']++;
                    $db->update('image_import_files', [
                        'status' => 'SKIPPED',
                        'errorMessage' => 'Could not extract accession number from filename: ' . $file['originalName']
                    ], ['id' => $file['id']]);
                    continue;
                }
                
                // Find book by accession number with enhanced search
                $book = $db->fetch("SELECT id, title, accessionNo FROM books WHERE accessionNo = ? AND isDeleted = 0", [$accessionNo]);

                if (!$book) {
                    // Try alternative search patterns for better matching
                    $alternativePatterns = [
                        str_replace('MVKL', 'MVKL0', $accessionNo), // Try with extra zero
                        str_replace('MVKL0', 'MVKL', $accessionNo), // Try without extra zero
                        preg_replace('/^MVKL0*/', 'MVKL', $accessionNo) // Remove leading zeros
                    ];

                    foreach ($alternativePatterns as $pattern) {
                        if ($pattern !== $accessionNo) {
                            $book = $db->fetch("SELECT id, title, accessionNo FROM books WHERE accessionNo = ? AND isDeleted = 0", [$pattern]);
                            if ($book) {
                                log_activity("Found book using alternative pattern: $accessionNo -> $pattern", 'INFO');
                                break;
                            }
                        }
                    }
                }

                if (!$book) {
                    $stats['skipped']++;
                    $db->update('image_import_files', [
                        'status' => 'SKIPPED',
                        'errorMessage' => "Book with accession number '$accessionNo' not found in database"
                    ], ['id' => $file['id']]);
                    continue;
                }

                // Check if image already exists
                try {
                    $existingImage = get_book_image_url($book['id'], false);
                    if ($existingImage && !$job['overwriteExisting']) {
                        $stats['skipped']++;
                        $db->update('image_import_files', [
                            'status' => 'SKIPPED',
                            'errorMessage' => 'Image already exists and overwrite is disabled'
                        ], ['id' => $file['id']]);
                        continue;
                    }
                } catch (Exception $e) {
                    log_activity("Error checking existing image for book {$book['id']}: " . $e->getMessage(), 'WARNING');
                    // Continue processing even if we can't check existing image
                }
                
                // Validate image file before processing
                $imageInfo = @getimagesize($file['filePath']);
                if ($imageInfo === false) {
                    $stats['errors']++;
                    $db->update('image_import_files', [
                        'status' => 'ERROR',
                        'errorMessage' => 'File is not a valid image or is corrupted'
                    ], ['id' => $file['id']]);
                    continue;
                }

                // Check minimum dimensions
                if ($imageInfo[0] < 100 || $imageInfo[1] < 100) {
                    $stats['skipped']++;
                    $db->update('image_import_files', [
                        'status' => 'SKIPPED',
                        'errorMessage' => "Image too small: {$imageInfo[0]}x{$imageInfo[1]} (minimum: 100x100)"
                    ], ['id' => $file['id']]);
                    continue;
                }

                // Process and save image
                $imageFile = [
                    'tmp_name' => $file['filePath'],
                    'name' => $file['originalName'],
                    'error' => UPLOAD_ERR_OK,
                    'size' => $fileSize,
                    'type' => $imageInfo['mime'] ?? 'image/jpeg'
                ];

                try {
                    $result = upload_book_image($imageFile, $book['id']);

                    if ($result['success']) {
                        $stats['matched']++;
                        $db->update('image_import_files', [
                            'status' => 'COMPLETED',
                            'bookId' => $book['id'],
                            'accessionNo' => $accessionNo
                        ], ['id' => $file['id']]);

                        log_activity("Successfully imported image for book: {$book['title']} ({$accessionNo})", 'INFO');
                    } else {
                        $stats['errors']++;
                        $errorMsg = $result['error'] ?? 'Unknown error during image upload';
                        $db->update('image_import_files', [
                            'status' => 'ERROR',
                            'errorMessage' => $errorMsg
                        ], ['id' => $file['id']]);

                        log_activity("Failed to import image for book {$book['id']}: $errorMsg", 'ERROR');
                    }
                } catch (Exception $e) {
                    $stats['errors']++;
                    $errorMsg = 'Image processing failed: ' . $e->getMessage();
                    $db->update('image_import_files', [
                        'status' => 'ERROR',
                        'errorMessage' => $errorMsg
                    ], ['id' => $file['id']]);

                    log_activity("Exception during image import for book {$book['id']}: " . $e->getMessage(), 'ERROR');
                }
                
            } catch (Exception $e) {
                $stats['errors']++;
                $errorMsg = 'Unexpected error processing file: ' . $e->getMessage();
                $db->update('image_import_files', [
                    'status' => 'ERROR',
                    'errorMessage' => $errorMsg
                ], ['id' => $file['id']]);

                log_activity("Unexpected error processing file {$file['originalName']}: " . $e->getMessage(), 'ERROR');
            }
        }

        // Determine final job status
        $finalStatus = 'COMPLETED';
        if ($stats['processed'] === 0) {
            $finalStatus = 'FAILED';
        } elseif ($stats['matched'] === 0 && $stats['errors'] > 0) {
            $finalStatus = 'FAILED';
        }

        // Update job status with comprehensive information
        $updateData = [
            'status' => $finalStatus,
            'processedFiles' => $stats['processed'],
            'matchedFiles' => $stats['matched'],
            'errorFiles' => $stats['errors'],
            'skippedFiles' => $stats['skipped'],
            'endTime' => date('Y-m-d H:i:s'),
            'updatedAt' => date('Y-m-d H:i:s')
        ];

        if ($finalStatus === 'FAILED' && $stats['processed'] === 0) {
            $updateData['errorMessage'] = 'No files were processed successfully';
        }

        $db->update('image_import_jobs', $updateData, ['id' => $jobId]);

        log_activity("Image import job completed: $jobId - Status: $finalStatus, Matched: {$stats['matched']}, Errors: {$stats['errors']}, Skipped: {$stats['skipped']}", 'INFO');

    } catch (Exception $e) {
        $errorMsg = 'Job processing failed: ' . $e->getMessage();

        try {
            $db->update('image_import_jobs', [
                'status' => 'FAILED',
                'errorMessage' => $errorMsg,
                'endTime' => date('Y-m-d H:i:s'),
                'updatedAt' => date('Y-m-d H:i:s')
            ], ['id' => $jobId]);
        } catch (Exception $updateError) {
            log_activity("Failed to update job status after error: " . $updateError->getMessage(), 'ERROR');
        }

        log_activity("Image import job failed: $jobId - " . $e->getMessage(), 'ERROR');
        throw $e; // Re-throw for higher level handling
    }
}

/**
 * Extract accession number from filename
 */
function extractAccessionFromFilename($filename, $method) {
    $basename = pathinfo($filename, PATHINFO_FILENAME);
    
    switch ($method) {
        case 'filename':
            // Look for MVKL pattern first
            if (preg_match('/MVKL\d+(-\d+)?/i', $basename, $matches)) {
                return strtoupper($matches[0]);
            }
            // Look for any number pattern
            if (preg_match('/\d+/', $basename, $matches)) {
                return 'MVKL' . str_pad($matches[0], 3, '0', STR_PAD_LEFT);
            }
            break;
            
        case 'exact':
            // Filename must exactly match accession number
            if (preg_match('/^MVKL\d+(-\d+)?$/i', $basename)) {
                return strtoupper($basename);
            }
            break;
    }
    
    return null;
}
?>

$page_title = 'Import Images - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">

    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>

    <style>
        /* Image Import Specific Styles */
        .image-drop-zone {
            border: 2px dashed var(--admin-border);
            border-radius: var(--radius-md);
            padding: 2rem;
            text-align: center;
            transition: all var(--transition-fast);
            background: var(--secondary-color);
            cursor: pointer;
            margin-bottom: 1rem;
        }

        .image-drop-zone.drag-over {
            border-color: var(--primary-color);
            background: rgba(85, 8, 12, 0.05);
        }

        .image-drop-zone:hover {
            border-color: var(--text-muted);
            background: var(--white);
        }

        .image-preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .image-preview-item {
            position: relative;
            border: 1px solid var(--admin-border);
            border-radius: var(--radius-sm);
            overflow: hidden;
            background: var(--white);
        }

        .image-preview-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .image-preview-item .filename {
            padding: 0.5rem;
            font-size: 0.75rem;
            color: var(--text-muted);
            border-top: 1px solid var(--admin-border);
            word-break: break-all;
        }

        .image-preview-item .remove-btn {
            position: absolute;
            top: 4px;
            right: 4px;
            background: rgba(239, 68, 68, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .matching-method-info {
            background: rgba(85, 8, 12, 0.05);
            border: 1px solid rgba(85, 8, 12, 0.2);
            border-radius: var(--radius-sm);
            padding: 1rem;
            margin-top: 1rem;
        }

        .file-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .file-stat {
            text-align: center;
            padding: 0.75rem;
            background: var(--secondary-color);
            border-radius: var(--radius-sm);
            border: 1px solid var(--admin-border);
        }

        .file-stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-dark);
        }

        .file-stat-label {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .import-status .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: var(--secondary-color);
            border-radius: var(--radius-sm);
            border: 1px solid var(--admin-border);
        }

        .status-label {
            font-weight: 600;
            color: var(--text-dark);
        }

        .status-value {
            color: var(--text-muted);
        }

        .status-pending { color: #f59e0b; }
        .status-processing { color: #3b82f6; }
        .status-completed { color: #10b981; }
        .status-failed { color: #ef4444; }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            border-radius: var(--radius-sm);
            border: 1px solid var(--admin-border);
        }

        .stat-item.success { background: rgba(16, 185, 129, 0.1); border-color: #10b981; }
        .stat-item.warning { background: rgba(245, 158, 11, 0.1); border-color: #f59e0b; }
        .stat-item.error { background: rgba(239, 68, 68, 0.1); border-color: #ef4444; }
        .stat-item.info { background: rgba(59, 130, 246, 0.1); border-color: #3b82f6; }

        .stat-number {
            display: block;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.status-pending { background: #fef3c7; color: #92400e; }
        .status-badge.status-processing { background: #dbeafe; color: #1e40af; }
        .status-badge.status-completed { background: #d1fae5; color: #065f46; }
        .status-badge.status-failed { background: #fee2e2; color: #991b1b; }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link active">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">📂</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="../database_reset.php" class="nav-link" style="color: #ef4444;">
                            <span class="nav-icon">🗑️</span>
                            Database Reset
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">🖼️</span>
                            Import Images
                        </h1>
                        <p class="header-subtitle">Upload and manage book cover images</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-separator">›</span>
                    <span class="breadcrumb-item active">Import Images</span>
                </nav>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        <?= htmlspecialchars($success) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'upload'): ?>
                    <!-- Upload Form -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span style="margin-right: 0.5rem;">🖼️</span>
                                Upload Book Cover Images
                            </h2>
                            <p class="card-description">Upload multiple book cover images to automatically match with books</p>
                        </div>
                        <div class="card-body">
                            <?php if (!$tablesExist): ?>
                                <div class="alert alert-warning">
                                    <span class="alert-icon">⚠️</span>
                                    Image import tables are not set up yet.
                                    <a href="setup_image_import.php" class="btn btn-sm btn-primary" style="margin-left: 1rem;">
                                        Setup Now
                                    </a>
                                </div>
                            <?php else: ?>
                            <form method="POST" enctype="multipart/form-data" class="form" id="image-upload-form">
                                <input type="hidden" name="action" value="upload">

                                <!-- Image Drop Zone -->
                                <div class="form-group">
                                    <label class="form-label">Book Cover Images *</label>
                                    <div class="image-drop-zone" id="image-drop-zone">
                                        <div class="drop-zone-content">
                                            <div style="font-size: 3rem; margin-bottom: 1rem;">🖼️</div>
                                            <h3 style="margin: 0 0 0.5rem 0; color: #374151;">Drop images here or click to browse</h3>
                                            <p style="margin: 0; color: #6b7280; font-size: 0.875rem;">
                                                Supported formats: JPG, PNG, WebP • Max size: <?= MAX_FILE_SIZE / 1024 / 1024 ?>MB per file
                                            </p>
                                        </div>
                                        <input type="file"
                                               id="image_files"
                                               name="image_files[]"
                                               accept="image/jpeg,image/jpg,image/png,image/webp"
                                               multiple
                                               required
                                               style="display: none;">
                                    </div>

                                    <!-- Image Preview Grid -->
                                    <div class="image-preview-grid" id="image-preview-grid" style="display: none;"></div>

                                    <!-- File Statistics -->
                                    <div class="file-stats" id="file-stats" style="display: none;">
                                        <div class="file-stat">
                                            <span class="file-stat-number" id="total-files">0</span>
                                            <span class="file-stat-label">Total Files</span>
                                        </div>
                                        <div class="file-stat">
                                            <span class="file-stat-number" id="valid-files">0</span>
                                            <span class="file-stat-label">Valid Images</span>
                                        </div>
                                        <div class="file-stat">
                                            <span class="file-stat-number" id="total-size">0 MB</span>
                                            <span class="file-stat-label">Total Size</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Matching Method -->
                                <div class="form-group">
                                    <label for="matching_method" class="form-label">Filename Matching Method</label>
                                    <select id="matching_method" name="matching_method" class="form-control">
                                        <option value="filename">Smart matching (extract from filename)</option>
                                        <option value="exact">Exact match (filename = accession number)</option>
                                    </select>
                                    <div class="form-help">
                                        Choose how to match image filenames with book accession numbers.
                                    </div>
                                </div>

                                <!-- Matching Method Info -->
                                <div class="matching-method-info" id="matching-info">
                                    <h4 style="margin: 0 0 0.5rem 0; color: #0369a1;">Smart Matching Examples:</h4>
                                    <ul style="margin: 0; padding-left: 1.25rem; color: #0369a1; font-size: 0.875rem;">
                                        <li><code>MVKL001.jpg</code> → matches book with accession <code>MVKL001</code></li>
                                        <li><code>book_MVKL123_cover.png</code> → matches book with accession <code>MVKL123</code></li>
                                        <li><code>456.jpg</code> → matches book with accession <code>MVKL456</code></li>
                                        <li><code>cover_789.png</code> → matches book with accession <code>MVKL789</code></li>
                                    </ul>
                                </div>

                                <!-- Options -->
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="overwrite_existing" value="1">
                                        <span class="checkbox-text">Overwrite existing images</span>
                                    </label>
                                    <div class="form-help">
                                        If enabled, existing book cover images will be replaced with new uploads.
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary" id="upload-btn" disabled>
                                        <span class="btn-icon">📤</span>
                                        Upload and Process Images
                                    </button>
                                    <a href="books.php" class="btn btn-secondary">
                                        <span class="btn-icon">📚</span>
                                        Back to Books
                                    </a>
                                </div>
                            </form>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'status' && $importJob): ?>
                    <!-- Import Status -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span style="margin-right: 0.5rem;">📊</span>
                                Image Import Status
                            </h2>
                            <p class="card-description">Job ID: <?= htmlspecialchars($importJob['id']) ?></p>
                        </div>
                        <div class="card-body">
                            <div class="import-status">
                                <div class="status-grid">
                                    <div class="status-item">
                                        <span class="status-label">Status:</span>
                                        <span class="status-value status-<?= strtolower($importJob['status']) ?>">
                                            <?= htmlspecialchars($importJob['status']) ?>
                                        </span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Created:</span>
                                        <span class="status-value"><?= date('Y-m-d H:i:s', strtotime($importJob['createdAt'])) ?></span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Matching Method:</span>
                                        <span class="status-value"><?= htmlspecialchars($importJob['matchingMethod']) ?></span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Overwrite:</span>
                                        <span class="status-value"><?= $importJob['overwriteExisting'] ? 'Yes' : 'No' ?></span>
                                    </div>
                                </div>

                                <?php if ($importJob['status'] === 'PENDING'): ?>
                                    <div class="status-actions">
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="process">
                                            <button type="submit" class="btn btn-primary">
                                                <span class="btn-icon">▶️</span>
                                                Start Processing
                                            </button>
                                        </form>
                                    </div>
                                <?php elseif ($importJob['status'] === 'PROCESSING'): ?>
                                    <div class="status-message">
                                        <span class="status-icon">⏳</span>
                                        Processing images... Please wait.
                                    </div>
                                    <script>
                                        // Auto-refresh every 5 seconds during processing
                                        setTimeout(function() {
                                            window.location.reload();
                                        }, 5000);
                                    </script>
                                <?php elseif ($importJob['status'] === 'COMPLETED'): ?>
                                    <div class="import-stats">
                                        <h3>Import Statistics</h3>
                                        <div class="stats-grid">
                                            <div class="stat-item success">
                                                <span class="stat-number"><?= $importJob['matchedFiles'] ?></span>
                                                <span class="stat-label">Images Matched</span>
                                            </div>
                                            <div class="stat-item warning">
                                                <span class="stat-number"><?= $importJob['skippedFiles'] ?></span>
                                                <span class="stat-label">Files Skipped</span>
                                            </div>
                                            <div class="stat-item error">
                                                <span class="stat-number"><?= $importJob['errorFiles'] ?></span>
                                                <span class="stat-label">Errors</span>
                                            </div>
                                            <div class="stat-item info">
                                                <span class="stat-number"><?= $importJob['totalFiles'] ?></span>
                                                <span class="stat-label">Total Files</span>
                                            </div>
                                        </div>
                                    </div>
                                <?php elseif ($importJob['status'] === 'FAILED'): ?>
                                    <div class="status-error">
                                        <span class="status-icon">❌</span>
                                        <div class="error-details">
                                            <h4>Import Failed</h4>
                                            <?php if ($importJob['errorMessage']): ?>
                                                <p><?= htmlspecialchars($importJob['errorMessage']) ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="card-actions">
                                <a href="import-images.php" class="btn btn-secondary">
                                    <span class="btn-icon">🖼️</span>
                                    New Import
                                </a>
                                <a href="books.php" class="btn btn-primary">
                                    <span class="btn-icon">📚</span>
                                    View Books
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Recent Import Jobs -->
                <?php if (!empty($recentJobs)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span style="margin-right: 0.5rem;">📋</span>
                                Recent Image Import Jobs
                            </h2>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Job ID</th>
                                            <th>Status</th>
                                            <th>Matched</th>
                                            <th>Errors</th>
                                            <th>Total Files</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentJobs as $job): ?>
                                            <tr>
                                                <td>
                                                    <code><?= htmlspecialchars($job['id']) ?></code>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?= strtolower($job['status']) ?>">
                                                        <?= htmlspecialchars($job['status']) ?>
                                                    </span>
                                                </td>
                                                <td><?= $job['matchedFiles'] ?? 0 ?></td>
                                                <td><?= $job['errorFiles'] ?? 0 ?></td>
                                                <td><?= $job['totalFiles'] ?? 0 ?></td>
                                                <td><?= date('M j, Y H:i', strtotime($job['createdAt'])) ?></td>
                                                <td>
                                                    <a href="import-images.php?action=status&job_id=<?= urlencode($job['id']) ?>"
                                                       class="btn btn-sm btn-secondary">
                                                        View
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Usage Guide -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <span style="margin-right: 0.5rem;">📖</span>
                            Image Import Guide
                        </h2>
                    </div>
                    <div class="card-body">
                        <div class="format-guide">
                            <h3>Filename Conventions</h3>
                            <div style="background: #eff6ff; padding: 1rem; border-radius: 0.5rem; border: 1px solid #dbeafe; margin-bottom: 1.5rem;">
                                <h4 style="font-weight: 600; color: #1e40af; margin: 0 0 0.5rem 0;">Smart Matching (Recommended)</h4>
                                <ul style="font-size: 0.875rem; color: #1e40af; margin: 0; padding-left: 1.25rem; line-height: 1.5;">
                                    <li><strong>MVKL001.jpg</strong> - Direct accession number match</li>
                                    <li><strong>book_MVKL123_cover.png</strong> - Extracts MVKL123</li>
                                    <li><strong>456.jpg</strong> - Converts to MVKL456</li>
                                    <li><strong>cover_789.png</strong> - Converts to MVKL789</li>
                                    <li><strong>MVKL100-02.jpg</strong> - Matches copy numbers</li>
                                </ul>
                            </div>

                            <div style="background: #fef2f2; padding: 1rem; border-radius: 0.5rem; border: 1px solid #fecaca; margin-bottom: 1.5rem;">
                                <h4 style="font-weight: 600; color: #991b1b; margin: 0 0 0.5rem 0;">Exact Matching</h4>
                                <ul style="font-size: 0.875rem; color: #dc2626; margin: 0; padding-left: 1.25rem; line-height: 1.5;">
                                    <li><strong>MVKL001.jpg</strong> - Filename must exactly match accession number</li>
                                    <li><strong>MVKL123.png</strong> - No additional text allowed</li>
                                    <li><strong>MVKL100-02.webp</strong> - Copy numbers supported</li>
                                </ul>
                            </div>

                            <h3>Tips for Best Results</h3>
                            <ul style="font-size: 0.875rem; color: #374151; line-height: 1.6; padding-left: 1.25rem;">
                                <li>Use high-quality images (minimum 300x400 pixels recommended)</li>
                                <li>Images will be automatically resized and compressed to ~30KB JPEG format</li>
                                <li>Supported formats: JPG, PNG, WebP</li>
                                <li>Maximum file size: <?= MAX_FILE_SIZE / 1024 / 1024 ?>MB per image</li>
                                <li>Ensure book records exist before uploading images</li>
                                <li>Use consistent naming conventions for easier matching</li>
                                <li>Enable "Overwrite existing" to replace current book cover images</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <script>
        // Mobile Menu Functionality - Define functions globally first
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');

            // Prevent body scroll when menu is open
            if (sidebar.classList.contains('open')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Initialize mobile menu functionality
        document.addEventListener('DOMContentLoaded', () => {
            // Mobile menu toggle button event listener
            const mobileToggle = document.getElementById('mobile-menu-toggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', toggleMobileMenu);
            }

            // Mobile overlay click event listener
            const mobileOverlay = document.getElementById('mobile-overlay');
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', closeMobileMenu);
            }

            // Mobile close button event listener
            const mobileCloseBtn = document.getElementById('mobile-close-btn');
            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', closeMobileMenu);
            }

            // Close mobile menu when clicking on nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) {
                        closeMobileMenu();
                    }
                });
            });
        });

        // Close mobile menu on window resize if screen becomes large
        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) {
                closeMobileMenu();
            }
        });

        // Handle escape key to close mobile menu
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });
    </script>

    <script>
        // Image upload functionality
        let selectedFiles = [];

        document.addEventListener('DOMContentLoaded', function() {
            const dropZone = document.getElementById('image-drop-zone');
            const fileInput = document.getElementById('image_files');
            const previewGrid = document.getElementById('image-preview-grid');
            const fileStats = document.getElementById('file-stats');
            const uploadBtn = document.getElementById('upload-btn');
            const matchingSelect = document.getElementById('matching_method');
            const matchingInfo = document.getElementById('matching-info');

            // Click to browse
            dropZone.addEventListener('click', () => fileInput.click());

            // Drag and drop events
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('drag-over');
            });

            dropZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
                handleFiles(e.dataTransfer.files);
            });

            // File input change
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });

            // Matching method change
            matchingSelect.addEventListener('change', updateMatchingInfo);

            function handleFiles(files) {
                selectedFiles = Array.from(files).filter(file => {
                    return file.type.startsWith('image/') &&
                           ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'].includes(file.type);
                });

                updateFileInput();
                updatePreview();
                updateStats();
                updateUploadButton();
            }

            function updateFileInput() {
                // Create new FileList for the input
                const dt = new DataTransfer();
                selectedFiles.forEach(file => dt.items.add(file));
                fileInput.files = dt.files;
            }

            function updatePreview() {
                previewGrid.innerHTML = '';

                if (selectedFiles.length === 0) {
                    previewGrid.style.display = 'none';
                    return;
                }

                previewGrid.style.display = 'grid';

                selectedFiles.forEach((file, index) => {
                    const item = document.createElement('div');
                    item.className = 'image-preview-item';

                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(file);
                    img.onload = () => URL.revokeObjectURL(img.src);

                    const filename = document.createElement('div');
                    filename.className = 'filename';
                    filename.textContent = file.name;

                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'remove-btn';
                    removeBtn.innerHTML = '×';
                    removeBtn.type = 'button';
                    removeBtn.onclick = () => removeFile(index);

                    item.appendChild(img);
                    item.appendChild(filename);
                    item.appendChild(removeBtn);
                    previewGrid.appendChild(item);
                });
            }

            function removeFile(index) {
                selectedFiles.splice(index, 1);
                updateFileInput();
                updatePreview();
                updateStats();
                updateUploadButton();
            }

            function updateStats() {
                if (selectedFiles.length === 0) {
                    fileStats.style.display = 'none';
                    return;
                }

                fileStats.style.display = 'grid';

                const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);
                const validFiles = selectedFiles.length;

                document.getElementById('total-files').textContent = selectedFiles.length;
                document.getElementById('valid-files').textContent = validFiles;
                document.getElementById('total-size').textContent = (totalSize / 1024 / 1024).toFixed(1) + ' MB';
            }

            function updateUploadButton() {
                uploadBtn.disabled = selectedFiles.length === 0;
            }

            function updateMatchingInfo() {
                const method = matchingSelect.value;
                const examples = {
                    'filename': [
                        '<code>MVKL001.jpg</code> → matches book with accession <code>MVKL001</code>',
                        '<code>book_MVKL123_cover.png</code> → matches book with accession <code>MVKL123</code>',
                        '<code>456.jpg</code> → matches book with accession <code>MVKL456</code>',
                        '<code>cover_789.png</code> → matches book with accession <code>MVKL789</code>'
                    ],
                    'exact': [
                        '<code>MVKL001.jpg</code> → matches book with accession <code>MVKL001</code>',
                        '<code>MVKL123.png</code> → matches book with accession <code>MVKL123</code>',
                        '<code>book_cover.jpg</code> → no match (not exact accession number)'
                    ]
                };

                const title = method === 'filename' ? 'Smart Matching Examples:' : 'Exact Matching Examples:';
                const exampleList = examples[method].map(ex => `<li>${ex}</li>`).join('');

                matchingInfo.innerHTML = `
                    <h4 style="margin: 0 0 0.5rem 0; color: #0369a1;">${title}</h4>
                    <ul style="margin: 0; padding-left: 1.25rem; color: #0369a1; font-size: 0.875rem;">
                        ${exampleList}
                    </ul>
                `;
            }

            // Initialize
            updateMatchingInfo();
        });
    </script>
</body>
</html>
