<?php
echo "<h1>PHP Upload Configuration Check</h1>";

echo "<h2>Current PHP Settings:</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Setting</th><th>Current Value</th><th>Recommended</th><th>Status</th></tr>";

$settings = [
    'upload_max_filesize' => ['current' => ini_get('upload_max_filesize'), 'recommended' => '100M'],
    'post_max_size' => ['current' => ini_get('post_max_size'), 'recommended' => '100M'],
    'max_execution_time' => ['current' => ini_get('max_execution_time'), 'recommended' => '300'],
    'max_input_time' => ['current' => ini_get('max_input_time'), 'recommended' => '300'],
    'memory_limit' => ['current' => ini_get('memory_limit'), 'recommended' => '256M'],
    'max_file_uploads' => ['current' => ini_get('max_file_uploads'), 'recommended' => '50']
];

foreach ($settings as $setting => $values) {
    $current = $values['current'];
    $recommended = $values['recommended'];
    
    // Convert to bytes for comparison
    $currentBytes = convertToBytes($current);
    $recommendedBytes = convertToBytes($recommended);
    
    $status = $currentBytes >= $recommendedBytes ? '✅ OK' : '❌ Too Low';
    
    echo "<tr>";
    echo "<td><strong>$setting</strong></td>";
    echo "<td>$current</td>";
    echo "<td>$recommended</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>Upload Test:</h2>";
echo "<p>Maximum theoretical upload size: " . min(convertToBytes(ini_get('upload_max_filesize')), convertToBytes(ini_get('post_max_size'))) / 1024 / 1024 . " MB</p>";

if (isset($_POST['test_upload']) && isset($_FILES['test_file'])) {
    echo "<h3>Upload Test Results:</h3>";
    $file = $_FILES['test_file'];
    
    echo "<p><strong>File Name:</strong> " . htmlspecialchars($file['name']) . "</p>";
    echo "<p><strong>File Size:</strong> " . number_format($file['size'] / 1024 / 1024, 2) . " MB</p>";
    echo "<p><strong>Upload Error:</strong> " . getUploadErrorMessage($file['error']) . "</p>";
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        echo "<p style='color: green;'>✅ Upload successful!</p>";
    } else {
        echo "<p style='color: red;'>❌ Upload failed!</p>";
    }
}

echo "<h3>Test File Upload:</h3>";
echo "<form method='POST' enctype='multipart/form-data'>";
echo "<input type='file' name='test_file' accept='image/*'>";
echo "<input type='hidden' name='test_upload' value='1'>";
echo "<button type='submit'>Test Upload</button>";
echo "</form>";

function convertToBytes($value) {
    $value = trim($value);
    $last = strtolower($value[strlen($value)-1]);
    $value = (int) $value;
    
    switch($last) {
        case 'g':
            $value *= 1024;
        case 'm':
            $value *= 1024;
        case 'k':
            $value *= 1024;
    }
    
    return $value;
}

function getUploadErrorMessage($error) {
    switch ($error) {
        case UPLOAD_ERR_OK:
            return 'No error';
        case UPLOAD_ERR_INI_SIZE:
            return 'File exceeds upload_max_filesize';
        case UPLOAD_ERR_FORM_SIZE:
            return 'File exceeds MAX_FILE_SIZE';
        case UPLOAD_ERR_PARTIAL:
            return 'File was only partially uploaded';
        case UPLOAD_ERR_NO_FILE:
            return 'No file was uploaded';
        case UPLOAD_ERR_NO_TMP_DIR:
            return 'Missing temporary folder';
        case UPLOAD_ERR_CANT_WRITE:
            return 'Failed to write file to disk';
        case UPLOAD_ERR_EXTENSION:
            return 'Upload stopped by extension';
        default:
            return 'Unknown error';
    }
}
?>
