[2025-06-13 07:47:45] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: books
[2025-06-13 07:47:45] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: authors
[2025-06-13 07:47:45] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: users
[2025-06-13 07:48:03] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: books
[2025-06-13 07:48:03] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: authors
[2025-06-13 07:48:03] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: users
[2025-06-13 07:51:02] [INFO] [admin@::1] Started CSV import: importingdata1 - Sheet1.csv
[2025-06-13 07:51:12] [INFO] [admin@::1] Started CSV import: importingdata1 - Sheet1.csv
[2025-06-13 07:51:29] [INFO] [admin@::1] Import job imp758443 completed successfully: 93 books imported
[2025-06-13 07:57:27] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 07:57:33] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 07:57:42] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 07:57:54] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 07:57:54] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 07:57:54] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 07:57:56] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 07:58:01] [ERROR] [admin@::1] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'maithili_library.image_import_jobs' doesn't exist SQL: 
    SELECT * FROM image_import_jobs
    ORDER BY createdAt DESC
    LIMIT 10

[2025-06-13 07:58:57] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: books
[2025-06-13 07:58:57] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: authors
[2025-06-13 07:58:57] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: users
[2025-06-13 07:59:34] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:04:33] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: books
[2025-06-13 08:04:33] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: authors
[2025-06-13 08:04:33] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: users
[2025-06-13 08:04:44] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: books
[2025-06-13 08:04:44] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: authors
[2025-06-13 08:04:44] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: users
[2025-06-13 08:08:50] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: books
[2025-06-13 08:08:50] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: authors
[2025-06-13 08:08:50] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: users
[2025-06-13 08:12:00] [INFO] [admin@::1] Started CSV import: importingdata1 - Sheet1.csv
[2025-06-13 08:12:06] [INFO] [admin@::1] Import job imp162185 completed successfully: 93 books imported
[2025-06-13 08:12:10] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:12:14] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:13:10] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:13:31] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:13:39] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:13:45] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:14:26] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:15:06] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:15:09] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:15:20] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:15:25] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:15:34] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:16:09] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:16:21] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:17:23] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:17:46] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:17:54] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:17:54] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_files
[2025-06-13 08:17:55] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:17:55] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_files
[2025-06-13 08:18:01] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:18:34] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:18:34] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_files
[2025-06-13 08:20:48] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_jobs
[2025-06-13 08:20:48] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: image_import_files
[2025-06-13 08:20:48] [ERROR] [admin@::1] Query failed: SQLSTATE[HY000]: General error: 1005 Can't create table `maithili_library`.`image_import_files` (errno: 121 "Duplicate key on write or update") SQL: ALTER TABLE `image_import_files` ADD CONSTRAINT `fk_image_import_files_jobId` FOREIGN KEY (`jobId`) REFERENCES `image_import_jobs` (`id`) ON DELETE CASCADE
[2025-06-13 08:20:48] [ERROR] [admin@::1] Query failed: SQLSTATE[HY000]: General error: 1005 Can't create table `maithili_library`.`image_import_files` (errno: 121 "Duplicate key on write or update") SQL: ALTER TABLE `image_import_files` ADD CONSTRAINT `fk_image_import_files_bookId` FOREIGN KEY (`bookId`) REFERENCES `books` (`id`) ON DELETE SET NULL
