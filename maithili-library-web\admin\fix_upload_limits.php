<?php
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in as admin
require_admin();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Upload Limits - मैथिली विकास कोष</title>
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    <style>
        .config-step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .config-step h3 {
            color: #495057;
            margin-top: 0;
        }
        .code-block {
            background: #f1f3f4;
            border: 1px solid #dadce0;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 0.5rem 0;
        }
        .status-good { color: #28a745; font-weight: bold; }
        .status-bad { color: #dc3545; font-weight: bold; }
        .current-settings {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include '../includes/admin_sidebar.php'; ?>
        
        <main class="admin-main">
            <div class="admin-header">
                <div class="admin-header-content">
                    <div class="admin-title-section">
                        <h1 class="admin-title">Fix Upload Limits</h1>
                        <p class="admin-subtitle">Configure PHP settings for large image uploads</p>
                    </div>
                </div>
            </div>

            <div class="admin-content">
                <div class="current-settings">
                    <h2>Current PHP Settings</h2>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr style="border-bottom: 1px solid #ddd;">
                            <th style="text-align: left; padding: 8px;">Setting</th>
                            <th style="text-align: left; padding: 8px;">Current</th>
                            <th style="text-align: left; padding: 8px;">Required</th>
                            <th style="text-align: left; padding: 8px;">Status</th>
                        </tr>
                        <?php
                        $settings = [
                            'upload_max_filesize' => ['current' => ini_get('upload_max_filesize'), 'required' => '100M'],
                            'post_max_size' => ['current' => ini_get('post_max_size'), 'required' => '100M'],
                            'max_execution_time' => ['current' => ini_get('max_execution_time'), 'required' => '300'],
                            'max_input_time' => ['current' => ini_get('max_input_time'), 'required' => '300'],
                            'max_file_uploads' => ['current' => ini_get('max_file_uploads'), 'required' => '50']
                        ];

                        foreach ($settings as $setting => $values) {
                            $current = $values['current'];
                            $required = $values['required'];
                            
                            // Simple comparison for most settings
                            $isOk = false;
                            if (in_array($setting, ['upload_max_filesize', 'post_max_size'])) {
                                $currentBytes = convertToBytes($current);
                                $requiredBytes = convertToBytes($required);
                                $isOk = $currentBytes >= $requiredBytes;
                            } else {
                                $isOk = (int)$current >= (int)$required;
                            }
                            
                            $statusClass = $isOk ? 'status-good' : 'status-bad';
                            $statusText = $isOk ? '✅ OK' : '❌ Needs Update';
                            
                            echo "<tr style='border-bottom: 1px solid #eee;'>";
                            echo "<td style='padding: 8px;'><strong>$setting</strong></td>";
                            echo "<td style='padding: 8px;'>$current</td>";
                            echo "<td style='padding: 8px;'>$required</td>";
                            echo "<td style='padding: 8px;' class='$statusClass'>$statusText</td>";
                            echo "</tr>";
                        }

                        function convertToBytes($value) {
                            $value = trim($value);
                            $last = strtolower($value[strlen($value)-1]);
                            $value = (int) $value;
                            
                            switch($last) {
                                case 'g': $value *= 1024;
                                case 'm': $value *= 1024;
                                case 'k': $value *= 1024;
                            }
                            
                            return $value;
                        }
                        ?>
                    </table>
                </div>

                <div class="config-step">
                    <h3>Step 1: Open XAMPP Control Panel</h3>
                    <p>1. Open your XAMPP Control Panel</p>
                    <p>2. Click the <strong>"Config"</strong> button next to Apache</p>
                    <p>3. Select <strong>"PHP (php.ini)"</strong> from the dropdown menu</p>
                </div>

                <div class="config-step">
                    <h3>Step 2: Update PHP Settings</h3>
                    <p>Find these lines in the php.ini file and update them:</p>
                    
                    <div class="code-block">
; File Uploads ;<br>
upload_max_filesize = 100M<br>
post_max_size = 100M<br>
max_file_uploads = 50<br><br>

; Resource Limits ;<br>
max_execution_time = 300<br>
max_input_time = 300
                    </div>
                    
                    <p><strong>Tip:</strong> Use Ctrl+F to search for each setting name in the php.ini file.</p>
                </div>

                <div class="config-step">
                    <h3>Step 3: Restart Apache</h3>
                    <p>1. Save the php.ini file</p>
                    <p>2. Go back to XAMPP Control Panel</p>
                    <p>3. Click <strong>"Stop"</strong> next to Apache</p>
                    <p>4. Wait a moment, then click <strong>"Start"</strong> to restart Apache</p>
                </div>

                <div class="config-step">
                    <h3>Step 4: Verify Changes</h3>
                    <p>After restarting Apache:</p>
                    <p>1. <a href="fix_upload_limits.php" class="btn btn-primary">Refresh this page</a> to check if settings updated</p>
                    <p>2. <a href="import-images.php" class="btn btn-secondary">Go to Image Import</a> to test uploading</p>
                </div>

                <div class="config-step" style="background: #e8f5e8; border-color: #c3e6c3;">
                    <h3>Alternative: Manual File Edit</h3>
                    <p>If the XAMPP method doesn't work:</p>
                    <p>1. Navigate to <code>C:\xampp\php\php.ini</code></p>
                    <p>2. Open the file in Notepad or any text editor</p>
                    <p>3. Make the same changes as shown in Step 2</p>
                    <p>4. Save and restart Apache</p>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
