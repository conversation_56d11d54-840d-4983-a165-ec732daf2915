<?php
require_once 'maithili-library-web/config/config.php';
require_once 'maithili-library-web/includes/Database.php';

echo "=== DEBUGGING ACCESSION NUMBER MATCHING ===\n\n";

try {
    $db = new Database();
    
    // Test the extraction function (updated version)
    function extractAccessionFromFilename($filename, $method) {
        global $db;

        $basename = pathinfo($filename, PATHINFO_FILENAME);

        switch ($method) {
            case 'filename':
                // Look for MVKL pattern first
                if (preg_match('/MVKL\d+(-\d+)?/i', $basename, $matches)) {
                    return strtoupper($matches[0]);
                }

                // Look for any number pattern and try to match with database
                if (preg_match('/\d+/', $basename, $matches)) {
                    $number = $matches[0];

                    // Try different accession number formats
                    $possibleAccessions = [
                        'MVKL' . str_pad($number, 3, '0', STR_PAD_LEFT) . '-01', // MVKL001-01
                        'MVKL' . str_pad($number, 3, '0', STR_PAD_LEFT),        // MVKL001
                        'MVKL' . $number . '-01',                               // MVKL1-01
                        'MVKL' . $number,                                       // MVKL1
                    ];

                    // Check which format exists in database
                    foreach ($possibleAccessions as $accession) {
                        try {
                            $exists = $db->fetch("SELECT accessionNo FROM books WHERE accessionNo = ? AND isDeleted = 0 LIMIT 1", [$accession]);
                            if ($exists) {
                                return $accession;
                            }
                        } catch (Exception $e) {
                            // Continue to next pattern if database error
                            continue;
                        }
                    }

                    // If no exact match found, return the most likely format based on database pattern
                    return 'MVKL' . str_pad($number, 3, '0', STR_PAD_LEFT) . '-01';
                }
                break;

            case 'smart':
                // Smart matching: try multiple patterns and check database
                $patterns = [
                    $basename,                                                  // Exact filename
                    'MVKL' . $basename,                                        // Add MVKL prefix
                    'MVKL' . $basename . '-01',                               // Add MVKL prefix and -01 suffix
                ];

                // If filename is numeric, try padded versions
                if (is_numeric($basename)) {
                    $patterns[] = 'MVKL' . str_pad($basename, 3, '0', STR_PAD_LEFT);
                    $patterns[] = 'MVKL' . str_pad($basename, 3, '0', STR_PAD_LEFT) . '-01';
                }

                foreach ($patterns as $pattern) {
                    try {
                        $exists = $db->fetch("SELECT accessionNo FROM books WHERE accessionNo = ? AND isDeleted = 0 LIMIT 1", [$pattern]);
                        if ($exists) {
                            return $pattern;
                        }
                    } catch (Exception $e) {
                        continue;
                    }
                }
                break;

            case 'exact':
                // Filename must exactly match accession number
                if (preg_match('/^MVKL\d+(-\d+)?$/i', $basename)) {
                    return strtoupper($basename);
                }
                break;
        }

        return null;
    }
    
    // Test with your filenames
    echo "1. Testing filename extraction (old method):\n";
    $testFiles = ['1.jpg', '2.png', '3.webp', '10.jpg', '100.jpg'];

    foreach ($testFiles as $filename) {
        $extracted = extractAccessionFromFilename($filename, 'filename');
        echo "   $filename -> $extracted\n";
    }

    echo "\n1b. Testing smart matching (new method):\n";
    foreach ($testFiles as $filename) {
        $extracted = extractAccessionFromFilename($filename, 'smart');
        echo "   $filename -> $extracted\n";
    }
    
    // Check what accession numbers exist in database
    echo "\n2. Sample accession numbers in database:\n";
    $accessions = $db->fetchAll("SELECT accessionNo FROM books WHERE isDeleted = 0 ORDER BY accessionNo LIMIT 20");
    
    foreach ($accessions as $acc) {
        echo "   " . $acc['accessionNo'] . "\n";
    }
    
    // Test matching with actual database
    echo "\n3. Testing database matching:\n";
    $testAccessions = ['MVKL001', 'MVKL002', 'MVKL003', 'MVKL010', 'MVKL100'];
    
    foreach ($testAccessions as $accNo) {
        $book = $db->fetch("SELECT id, title, accessionNo FROM books WHERE accessionNo = ? AND isDeleted = 0", [$accNo]);
        if ($book) {
            echo "   ✅ $accNo -> Found: {$book['title']}\n";
        } else {
            echo "   ❌ $accNo -> Not found\n";
        }
    }
    
    // Check for alternative patterns
    echo "\n4. Checking for alternative accession patterns:\n";
    $patterns = $db->fetchAll("
        SELECT DISTINCT accessionNo 
        FROM books 
        WHERE isDeleted = 0 
        AND (accessionNo LIKE 'MVKL%' OR accessionNo REGEXP '^[0-9]+$')
        ORDER BY accessionNo 
        LIMIT 10
    ");
    
    foreach ($patterns as $pattern) {
        echo "   " . $pattern['accessionNo'] . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n=== DEBUG COMPLETED ===\n";
?>
