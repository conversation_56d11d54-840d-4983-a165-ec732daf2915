<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Database Connection and Management Class
 */

class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $charset;
    private $conn;
    
    public function __construct() {
        $this->host = DB_HOST;
        $this->db_name = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;
    }
    
    /**
     * Get database connection
     */
    public function getConnection() {
        if ($this->conn === null) {
            try {
                $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset} COLLATE utf8mb4_unicode_ci"
                ];
                
                $this->conn = new PDO($dsn, $this->username, $this->password, $options);
                
                // Set timezone
                $this->conn->exec("SET time_zone = '+05:45'"); // Nepal timezone
                
            } catch (PDOException $e) {
                log_activity("Database connection failed: " . $e->getMessage(), 'ERROR');
                if (DEVELOPMENT_MODE) {
                    die("Database connection failed: " . $e->getMessage());
                } else {
                    die("Database connection failed. Please try again later.");
                }
            }
        }
        
        return $this->conn;
    }
    
    /**
     * Execute a query and return results
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            log_activity("Query failed: " . $e->getMessage() . " SQL: " . $sql, 'ERROR');
            throw $e;
        }
    }
    
    /**
     * Get single row
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Get all rows
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get single value
     */
    public function fetchColumn($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchColumn();
    }
    
    /**
     * Insert record and return last insert ID
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = str_repeat('?,', count($data) - 1) . '?';

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";

        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute(array_values($data));
            return $this->getConnection()->lastInsertId();
        } catch (PDOException $e) {
            log_activity("Insert failed: " . $e->getMessage() . " Table: " . $table, 'ERROR');
            throw $e;
        }
    }
    
    /**
     * Update record
     */
    public function update($table, $data, $where, $where_params = []) {
        $set_clause = [];
        foreach (array_keys($data) as $key) {
            $set_clause[] = "{$key} = ?";
        }
        $set_clause = implode(', ', $set_clause);

        // Handle both array-style and string-style where clauses for backward compatibility
        if (is_array($where)) {
            // Legacy format: $where is an associative array like ['id' => $value]
            $where_conditions = [];
            $where_values = [];
            foreach ($where as $key => $value) {
                $where_conditions[] = "{$key} = ?";
                $where_values[] = $value;
            }
            $where_clause = implode(' AND ', $where_conditions);
            $where_params = $where_values;
        } else {
            // New format: $where is a string like 'id = ?' and $where_params contains values
            $where_clause = $where;
        }

        $sql = "UPDATE {$table} SET {$set_clause} WHERE {$where_clause}";

        try {
            $stmt = $this->getConnection()->prepare($sql);
            // Use only positional parameters - merge data values with where parameters
            $params = array_merge(array_values($data), $where_params);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            log_activity("Update failed: " . $e->getMessage() . " Table: " . $table . " SQL: " . $sql, 'ERROR');
            throw $e;
        }
    }
    
    /**
     * Delete record (soft delete by default)
     */
    public function delete($table, $where, $where_params = [], $soft_delete = true) {
        if ($soft_delete) {
            return $this->update($table, ['isDeleted' => 1, 'updatedAt' => date('Y-m-d H:i:s')], $where, $where_params);
        } else {
            $sql = "DELETE FROM {$table} WHERE {$where}";
            try {
                $stmt = $this->getConnection()->prepare($sql);
                return $stmt->execute($where_params);
            } catch (PDOException $e) {
                log_activity("Delete failed: " . $e->getMessage() . " Table: " . $table, 'ERROR');
                throw $e;
            }
        }
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        return $this->getConnection()->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->getConnection()->rollback();
    }
    
    /**
     * Check if table exists
     */
    public function tableExists($table) {
        try {
            // Escape table name to prevent SQL injection
            $escapedTable = $this->getConnection()->quote($table);
            $sql = "SHOW TABLES LIKE $escapedTable";
            $stmt = $this->getConnection()->query($sql);
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            log_activity("Table exists check failed: " . $e->getMessage() . " Table: " . $table, 'ERROR');
            return false;
        }
    }
    
    /**
     * Get table structure
     */
    public function getTableStructure($table) {
        $sql = "DESCRIBE `{$table}`";
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            log_activity("Get table structure failed: " . $e->getMessage() . " Table: " . $table, 'ERROR');
            return [];
        }
    }
    
    /**
     * Create database backup
     */
    public function createBackup($filename = null) {
        if (!$filename) {
            $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $backup_file = BACKUP_DIR . $filename;
        
        // Use mysqldump command
        $command = sprintf(
            'mysqldump --host=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
            escapeshellarg($this->host),
            escapeshellarg($this->username),
            escapeshellarg($this->password),
            escapeshellarg($this->db_name),
            escapeshellarg($backup_file)
        );
        
        exec($command, $output, $return_code);
        
        if ($return_code === 0 && file_exists($backup_file)) {
            log_activity("Database backup created: " . $filename, 'INFO');
            return $backup_file;
        } else {
            log_activity("Database backup failed: " . $filename, 'ERROR');
            return false;
        }
    }
    
    /**
     * Restore database from backup
     */
    public function restoreBackup($backup_file) {
        if (!file_exists($backup_file)) {
            throw new Exception("Backup file not found: " . $backup_file);
        }
        
        $command = sprintf(
            'mysql --host=%s --user=%s --password=%s %s < %s',
            escapeshellarg($this->host),
            escapeshellarg($this->username),
            escapeshellarg($this->password),
            escapeshellarg($this->db_name),
            escapeshellarg($backup_file)
        );
        
        exec($command, $output, $return_code);
        
        if ($return_code === 0) {
            log_activity("Database restored from: " . basename($backup_file), 'INFO');
            return true;
        } else {
            log_activity("Database restore failed from: " . basename($backup_file), 'ERROR');
            return false;
        }
    }
    
    /**
     * Get database statistics for book management
     */
    public function getStats() {
        $stats = [];

        // Get table counts - focused on book management
        $tables = [
            'books' => 'Total Books',
            'authors' => 'Total Authors',
            'categories' => 'Total Categories',
            'languages' => 'Total Languages',
            'publishers' => 'Total Publishers',
            'subjects' => 'Total Subjects',
            'book_series' => 'Total Book Series',
            'locations' => 'Total Locations',
            'conditions' => 'Total Conditions',
            'sources' => 'Total Sources',
            'users' => 'Admin Users'
        ];

        foreach ($tables as $table => $label) {
            if ($this->tableExists($table)) {
                // Check if table has isDeleted column
                $columns = $this->getTableStructure($table);
                $has_isDeleted = false;
                foreach ($columns as $column) {
                    if ($column['Field'] === 'isDeleted') {
                        $has_isDeleted = true;
                        break;
                    }
                }

                if ($has_isDeleted) {
                    $count = $this->fetchColumn("SELECT COUNT(*) FROM {$table} WHERE isDeleted = 0");
                } else {
                    $count = $this->fetchColumn("SELECT COUNT(*) FROM {$table}");
                }

                $stats[$table] = [
                    'label' => $label,
                    'count' => (int)$count
                ];
            }
        }

        // Get available books count
        if ($this->tableExists('books')) {
            try {
                $available_books = $this->fetchColumn("SELECT COUNT(*) FROM books WHERE isDeleted = 0 AND isAvailable = 1");
                $stats['available_books'] = [
                    'label' => 'Available Books',
                    'count' => (int)$available_books
                ];
            } catch (Exception $e) {
                // If columns don't exist, skip this stat
            }
        }

        // Get books by language count (top 3)
        if ($this->tableExists('books') && $this->tableExists('languages')) {
            try {
                $language_stats = $this->fetchAll("
                    SELECT l.name, l.nameNepali, COUNT(b.id) as book_count
                    FROM languages l
                    LEFT JOIN books b ON l.id = b.languageId AND b.isDeleted = 0
                    WHERE l.isDeleted = 0
                    GROUP BY l.id, l.name, l.nameNepali
                    ORDER BY book_count DESC
                    LIMIT 3
                ");
                $stats['top_languages'] = $language_stats;
            } catch (Exception $e) {
                // If query fails, skip this stat
            }
        }

        // Get books by category count (top 3)
        if ($this->tableExists('books') && $this->tableExists('categories')) {
            try {
                $category_stats = $this->fetchAll("
                    SELECT c.name, c.nameNepali, COUNT(b.id) as book_count
                    FROM categories c
                    LEFT JOIN books b ON c.id = b.categoryId AND b.isDeleted = 0
                    WHERE c.isDeleted = 0
                    GROUP BY c.id, c.name, c.nameNepali
                    ORDER BY book_count DESC
                    LIMIT 3
                ");
                $stats['top_categories'] = $category_stats;
            } catch (Exception $e) {
                // If query fails, skip this stat
            }
        }

        return $stats;
    }
    
    /**
     * Close connection
     */
    public function close() {
        $this->conn = null;
    }
    
    /**
     * Destructor
     */
    public function __destruct() {
        $this->close();
    }
}
?>
